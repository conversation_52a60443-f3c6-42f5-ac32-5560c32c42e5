<!DOCTYPE html><html lang="zh-CN"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多区域服务QPS动态分配管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
        }
        
        body {
            background-color: #f6f8fb;
            color: #222;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 32px;
            height: 60px;
            background-color: #fff;
            border-bottom: 1px solid #e5eaf3;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        }
        
        .nav-items {
            display: flex;
            gap: 32px;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            padding: 0 10px;
            height: 60px;
            color: #222;
            text-decoration: none;
            font-weight: 500;
            font-size: 15px;
        }
        
        .nav-item.active {
            color: #2d78f4;
            border-bottom: 2px solid #2d78f4;
            background: #f0f6ff;
        }
        
        .user-area {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #888;
        }
        
        .breadcrumb {
            padding: 16px 32px;
            background-color: #f6f8fb;
            border-bottom: 1px solid #e5eaf3;
            display: flex;
            align-items: center;
            gap: 5px;
            color: #888;
        }
        
        .breadcrumb a {
            color: #2d78f4;
            text-decoration: none;
        }
        
        .main-content {
            padding: 32px;
        }
        
        .card {
            background-color: #fff;
            border-radius: 10px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            overflow: hidden;
            border: 1px solid #e5eaf3;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 18px 24px;
            border-bottom: 1px solid #e5eaf3;
        }
        
        .card-title {
            font-size: 17px;
            font-weight: 600;
            color: #222;
        }
        
        .card-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background-color: #2d78f4;
            color: white;
            box-shadow: 0 2px 4px rgba(45, 120, 244, 0.2);
        }
        
        .btn-primary:hover {
            background-color: #1c68e3;
            box-shadow: 0 4px 8px rgba(45, 120, 244, 0.3);
        }
        
        .btn-secondary {
            background-color: #f0f2f5;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e5e7eb;
        }
        
        .btn-add {
            background-color: #4cd964;
            box-shadow: 0 2px 4px rgba(76, 217, 100, 0.2);
            color: white;
        }
        
        .btn-add:hover {
            background-color: #3eba54;
            box-shadow: 0 4px 8px rgba(76, 217, 100, 0.3);
        }
        
        .btn-success {
            background-color: #4cd964;
            box-shadow: 0 2px 4px rgba(76, 217, 100, 0.2);
            color: white;
        }
        
        .btn-success:hover {
            background-color: #3eba54;
        }
        
        .btn-danger {
            background-color: #ff3b30;
            box-shadow: 0 2px 4px rgba(255, 59, 48, 0.2);
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #e12d23;
            box-shadow: 0 4px 8px rgba(255, 59, 48, 0.3);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th, .table td {
            padding: 14px 16px;
            text-align: left;
            border-bottom: 1px solid #eaedf3;
        }
        
        .table th {
            font-weight: 500;
            color: #666;
            background-color: #f9fafc;
        }
        
        .table-empty {
            padding: 30px;
            text-align: center;
            color: #999;
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        
        .status-online {
            background-color: #4cd964;
            box-shadow: 0 0 5px rgba(76, 217, 100, 0.5);
        }
        
        .status-offline {
            background-color: #ff3b30;
            box-shadow: 0 0 5px rgba(255, 59, 48, 0.5);
        }
        
        .qps-bar {
            height: 8px;
            width: 100%;
            background-color: #f0f2f5;
            border-radius: 4px;
            margin-top: 5px;
            overflow: hidden;
        }
        
        .qps-fill {
            height: 100%;
            background-color: #2d78f4;
            border-radius: 4px;
        }
        
        .qps-flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .qps-value {
            font-weight: bold;
            font-size: 18px;
            color: #333;
        }
        
        .qps-action {
            display: flex;
            gap: 8px;
        }
        
        .qps-input {
            background-color: #fff;
            border: 1px solid #d9e1ef;
            color: #333;
            padding: 5px 10px;
            border-radius: 4px;
            width: 100px;
        }
        
        .qps-total {
            padding: 15px 20px;
            background-color: #f9fafc;
            border: 1px solid #eaedf3;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        
        .card-content {
            padding: 20px;
        }
        
        /* 模态窗口样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal.active {
            display: flex;
        }
        
        .modal-content {
            background-color: #fff;
            border-radius: 4px;
            width: 500px;
            max-width: 90%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            border: 1px solid #eaedf3;
        }
        
        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eaedf3;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
        
        .modal-close {
            cursor: pointer;
            font-size: 20px;
            color: #999;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #eaedf3;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #666;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            background-color: #fff;
            border: 1px solid #d9e1ef;
            color: #333;
            padding: 8px 12px;
            border-radius: 6px;
        }

        .form-control:disabled {
            background-color: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
        }

        .form-checkbox input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
        
        .form-select {
            width: 100%;
            background-color: #fff;
            border: 1px solid #d9e1ef;
            color: #333;
            padding: 8px 12px;
            border-radius: 6px;
        }
        
        .region-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .region-card {
            background-color: #fff;
            border-radius: 4px;
            padding: 15px;
            width: calc(50% - 10px);
            border: 1px solid #eaedf3;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        }
        
        @media (max-width: 900px) {
            .region-card {
                width: 100%;
            }
        }
        
        .region-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .region-name {
            font-weight: 500;
            color: #333;
            font-size: 16px;
        }
        
        .region-actions {
            display: flex;
            gap: 10px;
        }
        
        .service-stats {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .service-item {
            background-color: #f9fafc;
            padding: 10px;
            border-radius: 4px;
            flex: 1;
            border: 1px solid #eaedf3;
        }
        
        .service-name {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .qps-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-bottom: 5px;
        }
        
        .qps-item-title {
            font-size: 12px;
            color: #888;
        }
        
        .qps-item-total {
            font-size: 12px;
            color: #888;
        }
        
        .qps-item-current {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .qps-stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .qps-stat-label {
            color: #666;
        }
        
        .qps-stat-value {
            color: #333;
            font-weight: 500;
        }
        
        .service-stats-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            margin-right: 5px;
        }
        
        .tag-face {
            background-color: #2d78f4;
            color: white;
        }
        
        .tag-feature {
            background-color: #4cd964;
            color: white;
        }
        
        .tag-liveness {
            background-color: #ff9500;
            color: white;
        }
        
        .qps-remaining {
            background-color: #f9fafc;
            border-radius: 4px;
            padding: 8px 12px;
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
            border: 1px solid #eaedf3;
        }
        
        .qps-remaining-label {
            color: #666;
        }
        
        .qps-remaining-value {
            color: #333;
            font-weight: 500;
        }
        
        .delete-confirm-content {
            display: flex;
            gap: 15px;
            padding: 10px 0;
        }
        
        .delete-warning-icon {
            font-size: 24px;
            color: #ff9500;
        }
        
        .delete-warning-text {
            flex: 1;
        }
        
        .delete-warning-detail {
            margin: 10px 0 5px;
            color: #8e94a3;
        }
        
        .delete-warning-list {
            margin: 0 0 10px;
            padding-left: 20px;
            color: #8e94a3;
        }
        
        .delete-warning-note {
            color: #ff3b30;
            font-weight: 500;
        }
        
        .qps-server-detail {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .qps-server-current {
            font-weight: 600;
            color: #333;
        }
        
        .qps-server-total {
            font-size: 12px;
            color: #888;
        }
        
        .qps-server-percent {
            font-size: 12px;
            color: #2d78f4;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <div class="nav-items">
            <a href="#" class="nav-item">
                <span>⋮</span>
                <span>首页</span>
            </a>
            <a href="#" class="nav-item">
                <span>★</span>
                <span>AI基础服务</span>
            </a>
            <a href="#" class="nav-item active">
                <span>⊞</span>
                <span>AI基础服务管理</span>
            </a>
            <a href="#" class="nav-item">
                <span>☑</span>
                <span>日志管理</span>
            </a>
            <a href="#" class="nav-item">
                <span>⚙</span>
                <span>系统管理</span>
            </a>
        </div>
        <div class="user-area">
            <span>admin</span>
        </div>
    </div>
    
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
        <a href="#">AI基础服务管理</a>
        <span>/</span>
        <span>多区域QPS分配</span>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
        <!-- QPS总量和概览 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">QPS资源总览</div>
                <div class="card-actions">
                    <button class="btn btn-secondary">刷新</button>
                </div>
            </div>
            <div class="card-content">
                <div class="service-stats-container">
                    <!-- kface QPS -->
                    <div class="qps-total">
                        <div class="qps-flex">
                            <div>
                                <div>人脸1:1 QPS总量</div>
                                <div class="qps-value">5,000</div>
                            </div>
                            <div>
                                <div>已分配QPS</div>
                                <div class="qps-value">3,500</div>
                            </div>
                            <div>
                                <div>剩余QPS</div>
                                <div class="qps-value">1,500</div>
                            </div>
                        </div>
                        <div class="qps-bar">
                            <div class="qps-fill" style="width: 70%;"></div>
                        </div>
                    </div>
                    
                    <!-- kfeature QPS -->
                    <div class="qps-total">
                        <div class="qps-flex">
                            <div>
                                <div>人脸1:N QPS总量</div>
                                <div class="qps-value">3,000</div>
                            </div>
                            <div>
                                <div>已分配QPS</div>
                                <div class="qps-value">2,400</div>
                            </div>
                            <div>
                                <div>剩余QPS</div>
                                <div class="qps-value">600</div>
                            </div>
                        </div>
                        <div class="qps-bar">
                            <div class="qps-fill" style="width: 80%;"></div>
                        </div>
                    </div>
                    
                    <!-- kliveness QPS -->
                    <div class="qps-total">
                        <div class="qps-flex">
                            <div>
                                <div>活体检测 QPS总量</div>
                                <div class="qps-value">2,000</div>
                            </div>
                            <div>
                                <div>已分配QPS</div>
                                <div class="qps-value">1,600</div>
                            </div>
                            <div>
                                <div>剩余QPS</div>
                                <div class="qps-value">400</div>
                            </div>
                        </div>
                        <div class="qps-bar">
                            <div class="qps-fill" style="width: 80%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 区域列表 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">区域_节点列表</div>
                <div class="card-actions">
                    <button class="btn btn-add" onclick="showAddRegionModal()">+ 添加区域</button>
                </div>
            </div>
            <div class="card-content">
                <!-- 区域卡片 -->
                <div class="region-cards">
                    <!-- 四区_北京 -->
                    <div class="region-card">
                        <div class="region-header">
                            <div class="region-name">四区_北京</div>
                            <div class="region-actions">
                                <button class="btn btn-primary" onclick="showQpsDistributionModal('四区_北京')">QPS分配</button>
                                <button class="btn btn-danger" onclick="confirmDeleteRegion('四区_北京')">删除区域</button>
                            </div>
                        </div>
                        <div class="service-stats">
                            <div class="service-item">
                                <div class="service-name">人脸1:1</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">1,800</div>
                                    <div class="qps-item-total">/ 5,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 36%;"></div>
                                </div>
                            </div>
                            <div class="service-item">
                                <div class="service-name">人脸1:N</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">1,000</div>
                                    <div class="qps-item-total">/ 3,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 33.3%;"></div>
                                </div>
                            </div>
                            <div class="service-item">
                                <div class="service-name">活体检测</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">800</div>
                                    <div class="qps-item-total">/ 2,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 40%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 四区_上海 -->
                    <div class="region-card">
                        <div class="region-header">
                            <div class="region-name">四区_上海</div>
                            <div class="region-actions">
                                <button class="btn btn-primary" onclick="showQpsDistributionModal('四区_上海')">QPS分配</button>
                                <button class="btn btn-danger" onclick="confirmDeleteRegion('四区_上海')">删除区域</button>
                            </div>
                        </div>
                        <div class="service-stats">
                            <div class="service-item">
                                <div class="service-name">人脸1:1</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">1,200</div>
                                    <div class="qps-item-total">/ 5,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 24%;"></div>
                                </div>
                            </div>
                            <div class="service-item">
                                <div class="service-name">人脸1:N</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">800</div>
                                    <div class="qps-item-total">/ 3,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 26.7%;"></div>
                                </div>
                            </div>
                            <div class="service-item">
                                <div class="service-name">活体检测</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">500</div>
                                    <div class="qps-item-total">/ 2,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 25%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 四区_广州 -->
                    <div class="region-card">
                        <div class="region-header">
                            <div class="region-name">四区_广州</div>
                            <div class="region-actions">
                                <button class="btn btn-primary" onclick="showQpsDistributionModal('四区_广州')">QPS分配</button>
                                <button class="btn btn-danger" onclick="confirmDeleteRegion('四区_广州')">删除区域</button>
                            </div>
                        </div>
                        <div class="service-stats">
                            <div class="service-item">
                                <div class="service-name">人脸1:1</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">500</div>
                                    <div class="qps-item-total">/ 5,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 10%;"></div>
                                </div>
                            </div>
                            <div class="service-item">
                                <div class="service-name">人脸1:N</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">600</div>
                                    <div class="qps-item-total">/ 3,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 20%;"></div>
                                </div>
                            </div>
                            <div class="service-item">
                                <div class="service-name">活体检测</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">300</div>
                                    <div class="qps-item-total">/ 2,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 15%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 五区 -->
                    <div class="region-card">
                        <div class="region-header">
                            <div class="region-name">五区</div>
                            <div class="region-actions">
                                <button class="btn btn-primary" onclick="showQpsDistributionModal('五区')">QPS分配</button>
                                <button class="btn btn-danger" onclick="confirmDeleteRegion('五区')">删除区域</button>
                            </div>
                        </div>
                        <div class="service-stats">
                            <div class="service-item">
                                <div class="service-name">人脸1:1</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">0</div>
                                    <div class="qps-item-total">/ 5,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 0%;"></div>
                                </div>
                            </div>
                            <div class="service-item">
                                <div class="service-name">人脸1:N</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">0</div>
                                    <div class="qps-item-total">/ 3,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 0%;"></div>
                                </div>
                            </div>
                            <div class="service-item">
                                <div class="service-name">活体检测</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">0</div>
                                    <div class="qps-item-total">/ 2,000</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-bar">
                                    <div class="qps-fill" style="width: 0%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- QPS分配表格 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">服务器QPS分配列表</div>
                <div class="card-actions">
                    <select class="qps-input" id="regionFilter">
                        <option value="">全部区域</option>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                    <select class="qps-input" id="serviceFilter">
                        <option value="">全部服务</option>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                    <input type="text" placeholder="服务器ID" class="qps-input">
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-secondary">重置</button>
                </div>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>服务器ID</th>
                        <th>区域</th>
                        <th>服务</th>
                        <th>已分配QPS / 总量</th>
                        <th>服务状态</th>
                        <th>授权下发状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>四区_北京_人脸1:1_192.168.1.200</td>
                        <td>未分配</td>
                        <td><span class="tag tag-face">人脸1:1</span></td>
                        <td>未分配</td>
                        <td><span class="status-indicator status-offline"></span>未绑定</td>
                        <td><span class="status-indicator status-offline"></span>未分配</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-primary" onclick="bindServer('四区_北京_人脸1:1_192.168.1.200')">绑定</button>
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_北京_人脸1:1_192.168.1.200')">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>四区_上海_人脸1:N_192.168.1.201</td>
                        <td>未分配</td>
                        <td><span class="tag tag-feature">人脸1:N</span></td>
                        <td>未分配</td>
                        <td><span class="status-indicator status-offline"></span>未绑定</td>
                        <td><span class="status-indicator status-offline"></span>未分配</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-primary" onclick="bindServer('四区_上海_人脸1:N_192.168.1.201')">绑定</button>
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_上海_人脸1:N_192.168.1.201')">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>四区_广州_活体检测_192.168.1.202</td>
                        <td>未分配</td>
                        <td><span class="tag tag-liveness">活体检测</span></td>
                        <td>未分配</td>
                        <td><span class="status-indicator status-offline"></span>未绑定</td>
                        <td><span class="status-indicator status-offline"></span>未分配</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-primary" onclick="bindServer('四区_广州_活体检测_192.168.1.202')">绑定</button>
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_广州_活体检测_192.168.1.202')">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>四区_北京_人脸1:1_192.168.1.101</td>
                        <td>四区_北京</td>
                        <td><span class="tag tag-face">人脸1:1</span></td>
                        <td>
                            <div class="qps-server-detail">
                                <div class="qps-server-current">500 QPS</div>
                                <div class="qps-server-total">区总量: 1800 QPS</div>
                                <div class="qps-server-percent">使用率: 27.8%</div>
                            </div>
                            <div class="qps-bar">
                                <div class="qps-fill" style="width: 27.8%;"></div>
                            </div>
                        </td>
                        <td><span class="status-indicator status-online"></span>正常</td>
                        <td><span class="status-indicator status-online"></span>成功</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-secondary" onclick="showEditNodeModal('四区_北京_人脸1:1_192.168.1.101')">编辑</button>
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_北京_人脸1:1_192.168.1.101')">解绑</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>四区_北京_人脸1:N_192.168.1.102</td>
                        <td>四区_北京</td>
                        <td><span class="tag tag-feature">人脸1:N</span></td>
                        <td>
                            <div class="qps-server-detail">
                                <div class="qps-server-current">800 QPS</div>
                                <div class="qps-server-total">区总量: 1000 QPS</div>
                                <div class="qps-server-percent">使用率: 80%</div>
                            </div>
                            <div class="qps-bar">
                                <div class="qps-fill" style="width: 80%;"></div>
                            </div>
                        </td>
                        <td><span class="status-indicator status-online"></span>正常</td>
                        <td><span class="status-indicator status-online"></span>成功</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-secondary" onclick="showEditNodeModal('四区_北京_人脸1:N_192.168.1.102')">编辑</button>
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_北京_人脸1:N_192.168.1.102')">解绑</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>四区_上海_活体检测_192.168.2.101</td>
                        <td>四区_上海</td>
                        <td><span class="tag tag-liveness">活体检测</span></td>
                        <td>
                            <div class="qps-server-detail">
                                <div class="qps-server-current">300 QPS</div>
                                <div class="qps-server-total">区总量: 500 QPS</div>
                                <div class="qps-server-percent">使用率: 60%</div>
                            </div>
                            <div class="qps-bar">
                                <div class="qps-fill" style="width: 60%;"></div>
                            </div>
                        </td>
                        <td><span class="status-indicator status-online"></span>正常</td>
                        <td><span class="status-indicator status-online"></span>成功</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-secondary" onclick="showEditNodeModal('四区_上海_活体检测_192.168.2.101')">编辑</button>
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_上海_活体检测_192.168.2.101')">解绑</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>四区_广州_人脸1:1_192.168.3.101</td>
                        <td>四区_广州</td>
                        <td><span class="tag tag-face">人脸1:1</span></td>
                        <td>
                            <div class="qps-server-detail">
                                <div class="qps-server-current">300 QPS</div>
                                <div class="qps-server-total">区总量: 500 QPS</div>
                                <div class="qps-server-percent">使用率: 60%</div>
                            </div>
                            <div class="qps-bar">
                                <div class="qps-fill" style="width: 60%;"></div>
                            </div>
                        </td>
                        <td><span class="status-indicator status-offline"></span>离线</td>
                        <td><span class="status-indicator status-offline"></span>失败</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-secondary" onclick="showEditNodeModal('四区_广州_人脸1:1_192.168.3.101')">编辑</button>
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_广州_人脸1:1_192.168.3.101')">解绑</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>四区_北京_活体检测_192.168.1.103</td>
                        <td>四区_北京</td>
                        <td><span class="tag tag-liveness">活体检测</span></td>
                        <td>
                            <div class="qps-server-detail">
                                <div class="qps-server-current">600 QPS</div>
                                <div class="qps-server-total">区总量: 800 QPS</div>
                                <div class="qps-server-percent">使用率: 75%</div>
                            </div>
                            <div class="qps-bar">
                                <div class="qps-fill" style="width: 75%;"></div>
                            </div>
                        </td>
                        <td><span class="status-indicator status-online"></span>正常</td>
                        <td><span class="status-indicator status-online"></span>成功</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-secondary" onclick="showEditNodeModal('四区_北京_活体检测_192.168.1.103')">编辑</button>
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_北京_活体检测_192.168.1.103')">解绑</button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 操作日志 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">操作日志</div>
                <div class="card-actions">
                    <select class="qps-input">
                        <option value="">全部区域</option>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                    <select class="qps-input">
                        <option value="">全部服务</option>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                    <input type="text" placeholder="服务器ID" class="qps-input">
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-secondary">导出</button>
                </div>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>日志ID</th>
                        <th>操作时间</th>
                        <th>区域</th>
                        <th>服务器ID</th>
                        <th>服务类型</th>
                        <th>操作类型</th>
                        <th>QPS变动</th>
                        <th>操作人</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>LOG00123</td>
                        <td>2025-04-08 14:32:15</td>
                        <td>四区_北京</td>
                        <td>四区_北京_人脸1:1_192.168.1.101</td>
                        <td>人脸1:1</td>
                        <td>QPS修改</td>
                        <td>500 → 800</td>
                        <td>admin</td>
                    </tr>
                    <tr>
                        <td>LOG00122</td>
                        <td>2025-04-08 14:30:10</td>
                        <td>四区_北京</td>
                        <td>四区_北京_人脸1:N_192.168.1.102</td>
                        <td>人脸1:N</td>
                        <td>QPS修改</td>
                        <td>800 → 600</td>
                        <td>admin</td>
                    </tr>
                    <tr>
                        <td>LOG00121</td>
                        <td>2025-04-08 14:28:05</td>
                        <td>四区_上海</td>
                        <td>四区_上海_活体检测_192.168.2.101</td>
                        <td>活体检测</td>
                        <td>服务器绑定</td>
                        <td>0 → 300</td>
                        <td>operator1</td>
                    </tr>
                    <tr>
                        <td>LOG00120</td>
                        <td>2025-04-08 14:25:18</td>
                        <td>四区_广州</td>
                        <td>四区_广州_人脸1:1_192.168.3.101</td>
                        <td>人脸1:1</td>
                        <td>服务器解绑</td>
                        <td>300 → 0</td>
                        <td>operator2</td>
                    </tr>
                    <tr>
                        <td>LOG00119</td>
                        <td>2025-04-08 14:20:45</td>
                        <td>四区_北京</td>
                        <td>四区_北京_活体检测_192.168.1.103</td>
                        <td>活体检测</td>
                        <td>QPS修改</td>
                        <td>400 → 600</td>
                        <td>admin</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 添加区域模态窗口 -->
    <div class="modal" id="addRegionModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">添加区域</div>
                <div class="modal-close" onclick="hideModal('addRegionModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">区域名称</label>
                    <input type="text" class="form-control" id="regionName" placeholder="请输入区域名称（如：四区_北京）">
                </div>
                <div class="form-group">
                    <label class="form-label">飞连 URL</label>
                    <input type="text" class="form-control" id="feilianUrl" placeholder="请输入飞连 URL">
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="enableFace11" onchange="toggleFace11Input()">
                        <label class="form-label" for="enableFace11">人脸1:1 QPS</label>
                        <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                            <span style="color: #8e94a3;">系统剩余可分配：</span>
                            <span style="color: #333; font-weight: 500;">1,500</span>
                        </div>
                    </div>
                    <input type="number" class="form-control" id="face11Qps" placeholder="请输入人脸1:1 QPS值" disabled>
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="enableFace1N" onchange="toggleFace1NInput()">
                        <label class="form-label" for="enableFace1N">人脸1:N QPS</label>
                        <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                            <span style="color: #8e94a3;">系统剩余可分配：</span>
                            <span style="color: #333; font-weight: 500;">600</span>
                        </div>
                    </div>
                    <input type="number" class="form-control" id="face1NQps" placeholder="请输入人脸1:N QPS值" disabled>
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="enableLiveness" onchange="toggleLivenessInput()">
                        <label class="form-label" for="enableLiveness">活体检测 QPS</label>
                        <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                            <span style="color: #8e94a3;">系统剩余可分配：</span>
                            <span style="color: #333; font-weight: 500;">400</span>
                        </div>
                    </div>
                    <input type="number" class="form-control" id="livenessQps" placeholder="请输入活体检测 QPS值" disabled>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('addRegionModal')">取消</button>
                <button class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>
    
    <!-- 添加节点模态窗口 -->
    <div class="modal" id="addNodeModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">添加服务器</div>
                <div class="modal-close" onclick="hideModal('addNodeModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">服务器ID</label>
                    <select class="form-select" id="addNodeId" onchange="handleNodeSelect()">
                        <option value="">请选择未分配服务器</option>
                        <option value="四区_北京_人脸1:1_192.168.1.105" data-service="人脸1:1">四区_北京_人脸1:1_192.168.1.105</option>
                        <option value="四区_上海_人脸1:N_192.168.2.105" data-service="人脸1:N">四区_上海_人脸1:N_192.168.2.105</option>
                        <option value="四区_广州_活体检测_192.168.3.105" data-service="活体检测">四区_广州_活体检测_192.168.3.105</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">所属区域</label>
                    <select class="form-select" id="addNodeRegion" onchange="updateAvailableQps()">
                        <option value="">请选择区域</option>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">服务类型</label>
                    <select class="form-select" id="addNodeService" disabled>
                        <option value="">请选择服务类型</option>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">QPS分配</label>
                    <input type="number" class="form-control" id="addNodeQps" placeholder="请输入QPS值">
                </div>
                <div class="qps-remaining">
                    <div class="qps-remaining-label">当前服务区域可分配QPS：</div>
                    <div class="qps-remaining-value" id="addNodeAvailableQps">-</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('addNodeModal')">取消</button>
                <button class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>
    
    <!-- 编辑节点模态窗口 -->
    <div class="modal" id="editNodeModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">编辑服务器</div>
                <div class="modal-close" onclick="hideModal('editNodeModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">服务器ID</label>
                    <input type="text" class="form-control" id="editNodeId" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">所属区域</label>
                    <select class="form-select" id="editNodeRegion" disabled>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">服务类型</label>
                    <select class="form-select" id="editNodeService" disabled>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">QPS分配</label>
                    <input type="number" class="form-control" id="editNodeQps" placeholder="请输入QPS值" onchange="validateEditQps()">
                </div>
                <div class="qps-remaining">
                    <div class="qps-remaining-label">当前服务区域可分配QPS：</div>
                    <div class="qps-remaining-value" id="editNodeAvailableQps">-</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('editNodeModal')">取消</button>
                <button class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>
    
    <!-- QPS分配模态窗口 -->
    <div class="modal" id="qpsDistributionModal">
        <div class="modal-content" style="width: 600px;">
            <div class="modal-header">
                <div class="modal-title" id="qpsDistributionTitle">区域QPS分配</div>
                <div class="modal-close" onclick="hideModal('qpsDistributionModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="service-stats-container">
                    <div class="form-group">
                        <label class="form-label">人脸1:1 QPS总量</label>
                        <input type="number" class="form-control" id="kfaceQps">
                        <div class="qps-remaining">
                            <div class="qps-remaining-label">系统剩余可分配人脸1:1 QPS：</div>
                            <div class="qps-remaining-value">1,500</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">人脸1:N QPS总量</label>
                        <input type="number" class="form-control" id="kfeatureQps">
                        <div class="qps-remaining">
                            <div class="qps-remaining-label">系统剩余可分配人脸1:N QPS：</div>
                            <div class="qps-remaining-value">600</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">活体检测 QPS总量</label>
                        <input type="number" class="form-control" id="klivenessQps">
                        <div class="qps-remaining">
                            <div class="qps-remaining-label">系统剩余可分配活体检测 QPS：</div>
                            <div class="qps-remaining-value">400</div>
                        </div>
                    </div>
                </div>
            
            <!-- 移除节点QPS分配表格 -->
            <!-- 
            <div style="margin: 20px 0; font-weight: bold;">节点QPS分配</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>节点ID</th>
                        <th>服务</th>
                        <th>已分配QPS</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="qpsDistributionTable">
                    
                </tbody>
            </table> 
            -->
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="hideModal('qpsDistributionModal')">关闭</button>
            <button class="btn btn-primary">保存分配</button> <!-- 此按钮现在应保存区域总QPS -->
        </div>
        </div>
    </div>
    
    <!-- 删除确认模态窗口 -->
    <div class="modal" id="deleteConfirmModal">
        <div class="modal-content" style="width: 400px;">
            <div class="modal-header">
                <div class="modal-title">确认删除</div>
                <div class="modal-close" onclick="hideModal('deleteConfirmModal')">×</div>
            </div>
            <div class="modal-body">
                <p>确定要删除服务器 <span id="deleteNodeId" style="font-weight: bold;"></span> 吗？此操作无法撤销。</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('deleteConfirmModal')">取消</button>
                <button class="btn btn-danger">确定删除</button>
            </div>
        </div>
    </div>

    <!-- 删除区域确认模态窗口 -->
    <div class="modal" id="deleteRegionConfirmModal">
        <div class="modal-content" style="width: 400px;">
            <div class="modal-header">
                <div class="modal-title">确认删除区域</div>
                <div class="modal-close" onclick="hideModal('deleteRegionConfirmModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="delete-confirm-content">
                    <div class="delete-warning-icon">⚠️</div>
                    <div class="delete-warning-text">
                        <p>您确定要删除区域 <span id="deleteRegionId" style="font-weight: bold; color: #ff3b30;"></span> 吗？</p>
                        <p class="delete-warning-detail">此操作将：</p>
                        <ul class="delete-warning-list">
                            <li>删除该区域下的所有服务器配置</li>
                            <li>释放该区域的所有QPS配额</li>
                            <li>删除该区域的所有授权记录</li>
                        </ul>
                        <p class="delete-warning-note">注意：此操作无法撤销，请谨慎操作！</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('deleteRegionConfirmModal')">取消</button>
                <button class="btn btn-danger" onclick="deleteRegion()">确定删除</button>
            </div>
        </div>
    </div>

    <script>
        // 模态窗口控制函数
        function showAddRegionModal() {
            document.getElementById('addRegionModal').classList.add('active');
        }
        
        function showAddNodeModal() {
            document.getElementById('addNodeModal').classList.add('active');
        }
        
        function showEditNodeModal(nodeId) {
            document.getElementById('editNodeId').value = nodeId;
            
            // 解析节点ID获取信息
            const [region1, region2, service] = nodeId.split('_');
            const region = region1 + '_' + region2;
            
            // 设置表单值
            document.getElementById('editNodeRegion').value = region;
            document.getElementById('editNodeService').value = service;
            
            // 设置当前QPS值
            let currentQps = 0;
            if (nodeId === '四区_北京_人脸1:1_192.168.1.101') {
                currentQps = 500;
            } else if (nodeId === '四区_北京_人脸1:N_192.168.1.102') {
                currentQps = 800;
            }
            document.getElementById('editNodeQps').value = currentQps;
            
            // 计算并显示可用QPS
            updateEditAvailableQps(nodeId, currentQps);
            
            // 显示模态窗口
            document.getElementById('editNodeModal').classList.add('active');
        }
        
        function updateEditAvailableQps(nodeId, currentQps) {
            const [region1, region2, service] = nodeId.split('_');
            const region = region1 + '_' + region2;
            
            const qpsLimits = {
                '人脸1:1': {
                    total: 5000,
                    used: {
                        '四区_北京': 1800,
                        '四区_上海': 1200,
                        '四区_广州': 500,
                        '五区': 0
                    }
                },
                '人脸1:N': {
                    total: 3000,
                    used: {
                        '四区_北京': 1000,
                        '四区_上海': 800,
                        '四区_广州': 600,
                        '五区': 0
                    }
                },
                '活体检测': {
                    total: 2000,
                    used: {
                        '四区_北京': 800,
                        '四区_上海': 500,
                        '四区_广州': 300,
                        '五区': 0
                    }
                }
            };
            
            if (region && service && qpsLimits[service]) {
                const totalQps = qpsLimits[service].total;
                const usedQps = Object.values(qpsLimits[service].used).reduce((a, b) => a + b, 0);
                // 可用QPS需要加上当前节点的QPS（因为是编辑）
                const availableQps = totalQps - usedQps + currentQps;
                document.getElementById('editNodeAvailableQps').textContent = availableQps;
            }
        }

        function validateEditQps() {
            const qpsInput = document.getElementById('editNodeQps');
            const availableQps = parseInt(document.getElementById('editNodeAvailableQps').textContent);
            const inputQps = parseInt(qpsInput.value);
            
            if (inputQps > availableQps) {
                alert('输入的QPS超过可分配的最大值！');
                qpsInput.value = availableQps;
            } else if (inputQps < 0) {
                alert('QPS不能为负数！');
                qpsInput.value = 0;
            }
        }
        
        function showQpsDistributionModal(regionId) {
            // 设置模态窗口标题
            document.getElementById('qpsDistributionTitle').textContent = regionId + ' QPS分配';
            
            // 填充QPS数据
            let kfaceQps = 0, kfeatureQps = 0, klivenessQps = 0;
            switch(regionId) {
                case '四区_北京': 
                    kfaceQps = 1800;
                    kfeatureQps = 1000;
                    klivenessQps = 800;
                    break;
                case '四区_上海': 
                    kfaceQps = 1200;
                    kfeatureQps = 800;
                    klivenessQps = 500;
                    break;
                case '四区_广州': 
                    kfaceQps = 500;
                    kfeatureQps = 600;
                    klivenessQps = 300;
                    break;
                case '五区':
                    kfaceQps = 0;
                    kfeatureQps = 0;
                    klivenessQps = 0;
                    break;
            }
            document.getElementById('kfaceQps').value = kfaceQps;
            document.getElementById('kfeatureQps').value = kfeatureQps;
            document.getElementById('klivenessQps').value = klivenessQps;
            
            // 移除填充表格数据的逻辑 (已在HTML中移除表格)

            // 显示模态窗口
            document.getElementById('qpsDistributionModal').classList.add('active');
        }
        
        function confirmDeleteNode(nodeId) {
            document.getElementById('deleteNodeId').textContent = nodeId;
            document.getElementById('deleteConfirmModal').classList.add('active');
        }
        
        function hideModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
            }
        }

        function handleNodeSelect() {
            const nodeSelect = document.getElementById('addNodeId');
            const serviceSelect = document.getElementById('addNodeService');
            
            if (nodeSelect.value) {
                const selectedOption = nodeSelect.options[nodeSelect.selectedIndex];
                const serviceType = selectedOption.getAttribute('data-service');
                
                // 设置服务类型
                serviceSelect.value = serviceType;
                
                // 从节点ID中提取区域信息并自动设置
                const regionParts = nodeSelect.value.split('_');
                const region = regionParts[0] + '_' + regionParts[1];
                document.getElementById('addNodeRegion').value = region;
                
                // 更新可用QPS
                updateAvailableQps();
            } else {
                serviceSelect.value = '';
            }
        }

        function updateAvailableQps() {
            const region = document.getElementById('addNodeRegion').value;
            const service = document.getElementById('addNodeService').value;
            let availableQps = 0;
            
            // 根据区域和服务类型计算可用QPS
            const qpsLimits = {
                '人脸1:1': {
                    total: 5000,
                    used: {
                        '四区_北京': 1800,
                        '四区_上海': 1200,
                        '四区_广州': 500,
                        '五区': 0
                    }
                },
                '人脸1:N': {
                    total: 3000,
                    used: {
                        '四区_北京': 1000,
                        '四区_上海': 800,
                        '四区_广州': 600,
                        '五区': 0
                    }
                },
                '活体检测': {
                    total: 2000,
                    used: {
                        '四区_北京': 800,
                        '四区_上海': 500,
                        '四区_广州': 300,
                        '五区': 0
                    }
                }
            };
            
            if (region && service && qpsLimits[service]) {
                const totalQps = qpsLimits[service].total;
                const usedQps = Object.values(qpsLimits[service].used).reduce((a, b) => a + b, 0);
                availableQps = totalQps - usedQps;
            }
            
            document.getElementById('addNodeAvailableQps').textContent = availableQps;
        }

        function confirmDeleteRegion(regionId) {
            document.getElementById('deleteRegionId').textContent = regionId;
            document.getElementById('deleteRegionConfirmModal').classList.add('active');
        }

        function deleteRegion() {
            const regionId = document.getElementById('deleteRegionId').textContent;
            // TODO: 实现删除区域的逻辑
            alert('区域 ' + regionId + ' 已删除');
            hideModal('deleteRegionConfirmModal');
        }

        function bindServer(serverId) {
            // 打开添加服务器模态窗口
            document.getElementById('addNodeModal').classList.add('active');
            // 自动选中服务器ID
            const addNodeIdSelect = document.getElementById('addNodeId');
            if (addNodeIdSelect) {
                for (let i = 0; i < addNodeIdSelect.options.length; i++) {
                    if (addNodeIdSelect.options[i].value === serverId) {
                        addNodeIdSelect.selectedIndex = i;
                        break;
                    }
                }
                // 触发change事件，自动填充服务类型和区域
                addNodeIdSelect.onchange && addNodeIdSelect.onchange();
            }
        }

        // 控制人脸1:1 QPS输入框的启用/禁用
        function toggleFace11Input() {
            const checkbox = document.getElementById('enableFace11');
            const input = document.getElementById('face11Qps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        // 控制人脸1:N QPS输入框的启用/禁用
        function toggleFace1NInput() {
            const checkbox = document.getElementById('enableFace1N');
            const input = document.getElementById('face1NQps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        // 控制活体检测 QPS输入框的启用/禁用
        function toggleLivenessInput() {
            const checkbox = document.getElementById('enableLiveness');
            const input = document.getElementById('livenessQps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }
    </script>


</body></html>