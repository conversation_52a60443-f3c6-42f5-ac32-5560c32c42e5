<!DOCTYPE html><html lang="zh-CN"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多区域服务QPS动态分配管理平台</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
        }
        
        body {
            background-color: #f6f8fb;
            color: #222;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 32px;
            height: 60px;
            background-color: #fff;
            border-bottom: 1px solid #e5eaf3;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        }
        
        .nav-items {
            display: flex;
            gap: 32px;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            padding: 0 10px;
            height: 60px;
            color: #222;
            text-decoration: none;
            font-weight: 500;
            font-size: 15px;
        }
        
        .nav-item.active {
            color: #2d78f4;
            border-bottom: 2px solid #2d78f4;
            background: #f0f6ff;
        }
        
        .user-area {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #888;
        }
        
        .breadcrumb {
            padding: 16px 32px;
            background-color: #f6f8fb;
            border-bottom: 1px solid #e5eaf3;
            display: flex;
            align-items: center;
            gap: 5px;
            color: #888;
        }
        
        .breadcrumb a {
            color: #2d78f4;
            text-decoration: none;
        }
        
        .main-content {
            padding: 32px;
        }
        
        .card {
            background-color: #fff;
            border-radius: 10px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            overflow: hidden;
            border: 1px solid #e5eaf3;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 18px 24px;
            border-bottom: 1px solid #e5eaf3;
        }
        
        .card-title {
            font-size: 17px;
            font-weight: 600;
            color: #222;
        }
        
        .card-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background-color: #2d78f4;
            color: white;
            box-shadow: 0 2px 4px rgba(45, 120, 244, 0.2);
        }
        
        .btn-primary:hover {
            background-color: #1c68e3;
            box-shadow: 0 4px 8px rgba(45, 120, 244, 0.3);
        }
        
        .btn-secondary {
            background-color: #f0f2f5;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e5e7eb;
        }
        
        .btn-add {
            background-color: #4cd964;
            box-shadow: 0 2px 4px rgba(76, 217, 100, 0.2);
            color: white;
        }
        
        .btn-add:hover {
            background-color: #3eba54;
            box-shadow: 0 4px 8px rgba(76, 217, 100, 0.3);
        }
        
        .btn-success {
            background-color: #4cd964;
            box-shadow: 0 2px 4px rgba(76, 217, 100, 0.2);
            color: white;
        }
        
        .btn-success:hover {
            background-color: #3eba54;
        }
        
        .btn-danger {
            background-color: #ff3b30;
            box-shadow: 0 2px 4px rgba(255, 59, 48, 0.2);
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #e12d23;
            box-shadow: 0 4px 8px rgba(255, 59, 48, 0.3);
        }

        .btn-adjust {
            background-color: #3b82f6;
            color: white;
            border: 1px solid #3b82f6;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }

        .btn-adjust:hover {
            background-color: #2563eb;
            border-color: #2563eb;
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }

        .btn-adjust.active {
            background-color: #f59e0b;
            border-color: #f59e0b;
            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
        }

        .btn-adjust.active:hover {
            background-color: #d97706;
            border-color: #d97706;
            box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th, .table td {
            padding: 14px 16px;
            text-align: left;
            border-bottom: 1px solid #eaedf3;
        }
        
        .table th {
            font-weight: 500;
            color: #666;
            background-color: #f9fafc;
        }
        
        .table-empty {
            padding: 30px;
            text-align: center;
            color: #999;
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        
        .status-online {
            background-color: #4cd964;
            box-shadow: 0 0 5px rgba(76, 217, 100, 0.5);
        }
        
        .status-offline {
            background-color: #ff3b30;
            box-shadow: 0 0 5px rgba(255, 59, 48, 0.5);
        }
        
        .qps-bar {
            height: 8px;
            width: 100%;
            background-color: #f0f2f5;
            border-radius: 4px;
            margin-top: 5px;
            overflow: hidden;
        }
        
        .qps-fill {
            height: 100%;
            background-color: #2d78f4;
            border-radius: 4px;
        }
        
        .qps-flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .qps-value {
            font-weight: bold;
            font-size: 18px;
            color: #333;
        }
        
        .qps-action {
            display: flex;
            gap: 8px;
        }
        
        .qps-input {
            background-color: #fff;
            border: 1px solid #d9e1ef;
            color: #333;
            padding: 5px 10px;
            border-radius: 4px;
            width: 100px;
        }
        
        .qps-total {
            padding: 15px 20px;
            background-color: #f9fafc;
            border: 1px solid #eaedf3;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        
        .card-content {
            padding: 20px;
        }
        
        /* 模态窗口样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal.active {
            display: flex;
        }
        
        .modal-content {
            background-color: #fff;
            border-radius: 4px;
            width: 500px;
            max-width: 90%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            border: 1px solid #eaedf3;
        }
        
        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eaedf3;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
        
        .modal-close {
            cursor: pointer;
            font-size: 20px;
            color: #999;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #eaedf3;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #666;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            background-color: #fff;
            border: 1px solid #d9e1ef;
            color: #333;
            padding: 8px 12px;
            border-radius: 6px;
        }

        .form-control:disabled {
            background-color: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
        }

        .form-checkbox input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
        
        .form-select {
            width: 100%;
            background-color: #fff;
            border: 1px solid #d9e1ef;
            color: #333;
            padding: 8px 12px;
            border-radius: 6px;
        }
        
        .region-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(480px, 1fr));
            gap: 24px;
        }

        .region-card {
            background: #fff;
            border: 1px solid #e5eaf3;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .region-card:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        @media (max-width: 900px) {
            .region-cards {
                grid-template-columns: 1fr;
            }
        }
        
        .region-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid #f0f3f7;
        }

        .region-name {
            font-weight: 700;
            color: #333;
            font-size: 18px;
        }
        
        .region-actions {
            display: flex;
            gap: 10px;
        }
        
        .service-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .service-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            transition: all 0.2s ease;
        }

        .service-item:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }
        
        .service-name {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .qps-item-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            margin-bottom: 12px;
        }

        .qps-item-current {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
        }

        .qps-item-percentage {
            font-size: 12px;
            color: #64748b;
            font-weight: 600;
            background: #f1f5f9;
            padding: 2px 8px;
            border-radius: 12px;
        }

        .qps-item-title {
            font-size: 11px;
            color: #64748b;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .qps-progress {
            width: 100%;
            height: 4px;
            background-color: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .qps-progress-fill {
            height: 100%;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .service-item[data-service="face11"] .qps-progress-fill {
            background: linear-gradient(90deg, #2d78f4, #1c68e3);
        }

        .service-item[data-service="face1n"] .qps-progress-fill {
            background: linear-gradient(90deg, #4cd964, #3eba54);
        }

        .service-item[data-service="liveness"] .qps-progress-fill {
            background: linear-gradient(90deg, #ff9500, #e6850e);
        }

        @media (max-width: 768px) {
            .service-stats {
                grid-template-columns: 1fr;
                gap: 12px;
            }
        }

        /* QPS调配模式样式 */
        .adjust-mode .service-item {
            border: 2px solid #e2e8f0;
            cursor: pointer;
            position: relative;
        }

        .adjust-mode .service-item:hover {
            border-color: #3b82f6;
            background: #f8faff;
        }

        .adjust-mode .service-item.selected {
            border-color: #2563eb;
            background: #eff6ff;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .qps-adjust-controls {
            display: none;
            position: absolute;
            top: -40px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10;
        }

        .adjust-mode .service-item.selected .qps-adjust-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .qps-adjust-input {
            width: 80px;
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
        }

        .qps-adjust-btn {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 11px;
            cursor: pointer;
        }

        .qps-adjust-btn:hover {
            background: #2563eb;
        }

        .qps-summary-panel {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            z-index: 1000;
        }

        .adjust-mode .qps-summary-panel {
            display: block;
        }

        .summary-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .summary-service {
            margin-bottom: 12px;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .summary-service-name {
            font-size: 13px;
            font-weight: 600;
            color: #64748b;
            margin-bottom: 8px;
        }

        .summary-stats {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }

        .summary-allocated {
            color: #1e293b;
            font-weight: 600;
        }

        .summary-remaining {
            color: #059669;
            font-weight: 600;
        }

        .summary-actions {
            margin-top: 16px;
            display: flex;
            gap: 8px;
        }

        .summary-btn {
            flex: 1;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
        }

        .summary-btn.primary {
            background: #3b82f6;
            color: white;
        }

        .summary-btn.secondary {
            background: #f1f5f9;
            color: #64748b;
        }
        
        .qps-stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .qps-stat-label {
            color: #666;
        }
        
        .qps-stat-value {
            color: #333;
            font-weight: 500;
        }
        
        .service-stats-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            margin-right: 5px;
        }
        
        .tag-face {
            background-color: #2d78f4;
            color: white;
        }
        
        .tag-feature {
            background-color: #4cd964;
            color: white;
        }
        
        .tag-liveness {
            background-color: #ff9500;
            color: white;
        }
        
        .qps-remaining {
            background-color: #f9fafc;
            border-radius: 4px;
            padding: 8px 12px;
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
            border: 1px solid #eaedf3;
        }
        
        .qps-remaining-label {
            color: #666;
        }
        
        .qps-remaining-value {
            color: #333;
            font-weight: 500;
        }
        
        .delete-confirm-content {
            display: flex;
            gap: 15px;
            padding: 10px 0;
        }
        
        .delete-warning-icon {
            font-size: 24px;
            color: #ff9500;
        }
        
        .delete-warning-text {
            flex: 1;
        }
        
        .delete-warning-detail {
            margin: 10px 0 5px;
            color: #8e94a3;
        }
        
        .delete-warning-list {
            margin: 0 0 10px;
            padding-left: 20px;
            color: #8e94a3;
        }
        
        .delete-warning-note {
            color: #ff3b30;
            font-weight: 500;
        }
        
        .qps-server-detail {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .qps-server-current {
            font-weight: 600;
            color: #333;
        }
        
        .qps-server-total {
            font-size: 12px;
            color: #888;
        }
        
        .qps-server-percent {
            font-size: 12px;
            color: #2d78f4;
            font-weight: 500;
        }

        /* 饼状图容器样式 */
        .chart-container {
            display: flex;
            gap: 30px;
            align-items: center;
            justify-content: space-around;
            flex-wrap: wrap;
        }

        .chart-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 280px;
            flex: 1;
        }

        .chart-wrapper {
            position: relative;
            width: 200px;
            height: 200px;
            margin-bottom: 15px;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            text-align: center;
        }

        .chart-stats {
            display: flex;
            flex-direction: column;
            gap: 8px;
            width: 100%;
            max-width: 200px;
        }

        .chart-stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 12px;
            background-color: #f9fafc;
            border-radius: 4px;
            border: 1px solid #eaedf3;
        }

        .chart-stat-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: #666;
        }

        .chart-stat-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        .chart-stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        @media (max-width: 1200px) {
            .chart-container {
                flex-direction: column;
                gap: 40px;
            }

            .chart-item {
                min-width: auto;
                width: 100%;
                max-width: 400px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <div class="nav-items">
            <a href="#" class="nav-item">
                <span>⋮</span>
                <span>首页</span>
            </a>
            <a href="#" class="nav-item">
                <span>★</span>
                <span>AI基础服务</span>
            </a>
            <a href="#" class="nav-item active">
                <span>⊞</span>
                <span>AI基础服务管理</span>
            </a>
            <a href="#" class="nav-item">
                <span>☑</span>
                <span>日志管理</span>
            </a>
            <a href="#" class="nav-item">
                <span>⚙</span>
                <span>系统管理</span>
            </a>
        </div>
        <div class="user-area">
            <span>admin</span>
        </div>
    </div>
    
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
        <a href="#">AI基础服务管理</a>
        <span>/</span>
        <span>多区域QPS分配</span>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
        <!-- QPS总量和概览 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">QPS资源总览</div>
                <div class="card-actions">
                    <button class="btn btn-secondary">刷新</button>
                </div>
            </div>
            <div class="card-content">
                <div class="chart-container">
                    <!-- 人脸1:1 QPS饼状图 -->
                    <div class="chart-item">
                        <div class="chart-title">人脸1:1 QPS分配</div>
                        <div class="chart-wrapper">
                            <canvas id="face11Chart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #2d78f4;"></div>
                                    <span>已分配</span>
                                </div>
                                <div class="chart-stat-value">3,500</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #e5eaf3;"></div>
                                    <span>剩余</span>
                                </div>
                                <div class="chart-stat-value">1,500</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #333;"></div>
                                    <span>总量</span>
                                </div>
                                <div class="chart-stat-value">5,000</div>
                            </div>
                        </div>
                    </div>

                    <!-- 人脸1:N QPS饼状图 -->
                    <div class="chart-item">
                        <div class="chart-title">人脸1:N QPS分配</div>
                        <div class="chart-wrapper">
                            <canvas id="face1NChart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #4cd964;"></div>
                                    <span>已分配</span>
                                </div>
                                <div class="chart-stat-value">2,400</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #e5eaf3;"></div>
                                    <span>剩余</span>
                                </div>
                                <div class="chart-stat-value">2,600</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #333;"></div>
                                    <span>总量</span>
                                </div>
                                <div class="chart-stat-value">5,000</div>
                            </div>
                        </div>
                    </div>

                    <!-- 活体检测 QPS饼状图 -->
                    <div class="chart-item">
                        <div class="chart-title">活体检测 QPS分配</div>
                        <div class="chart-wrapper">
                            <canvas id="livenessChart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #ff9500;"></div>
                                    <span>已分配</span>
                                </div>
                                <div class="chart-stat-value">1,600</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #e5eaf3;"></div>
                                    <span>剩余</span>
                                </div>
                                <div class="chart-stat-value">3,400</div>
                            </div>
                            <div class="chart-stat-item">
                                <div class="chart-stat-label">
                                    <div class="chart-stat-color" style="background-color: #333;"></div>
                                    <span>总量</span>
                                </div>
                                <div class="chart-stat-value">5,000</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 区域列表 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">区域列表</div>
                <div class="card-actions">
                    <button class="btn btn-adjust" onclick="toggleAdjustMode()" id="adjustModeBtn">🔄 QPS调配模式</button>
                    <button class="btn btn-add" onclick="showAddRegionModal()">+ 添加区域</button>
                </div>
            </div>
            <div class="card-content">
                <!-- 区域卡片 -->
                <div class="region-cards">
                    <!-- 四区_北京 -->
                    <div class="region-card">
                        <div class="region-header">
                            <div class="region-name">四区_北京</div>
                            <div class="region-actions">
                                <button class="btn btn-primary" onclick="showEditRegionModal('四区_北京')">编辑区域</button>
                                <button class="btn btn-danger" onclick="confirmDeleteRegion('四区_北京')">删除区域</button>
                            </div>
                        </div>
                        <div class="service-stats">
                            <div class="service-item" data-service="face11" data-region="四区_北京" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="1800" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">人脸1:1</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">1,800</div>
                                    <div class="qps-item-percentage">36%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 36%;"></div>
                                </div>
                            </div>
                            <div class="service-item" data-service="face1n" data-region="四区_北京" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="1000" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">人脸1:N</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">1,000</div>
                                    <div class="qps-item-percentage">20%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 20%;"></div>
                                </div>
                            </div>
                            <div class="service-item" data-service="liveness" data-region="四区_北京" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="800" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">活体检测</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">800</div>
                                    <div class="qps-item-percentage">16%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 16%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 四区_上海 -->
                    <div class="region-card">
                        <div class="region-header">
                            <div class="region-name">四区_上海</div>
                            <div class="region-actions">
                                <button class="btn btn-primary" onclick="showEditRegionModal('四区_上海')">编辑区域</button>
                                <button class="btn btn-danger" onclick="confirmDeleteRegion('四区_上海')">删除区域</button>
                            </div>
                        </div>
                        <div class="service-stats">
                            <div class="service-item" data-service="face11" data-region="四区_上海" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="1200" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">人脸1:1</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">1,200</div>
                                    <div class="qps-item-percentage">24%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 24%;"></div>
                                </div>
                            </div>
                            <div class="service-item" data-service="face1n" data-region="四区_上海" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="800" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">人脸1:N</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">800</div>
                                    <div class="qps-item-percentage">16%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 16%;"></div>
                                </div>
                            </div>
                            <div class="service-item" data-service="liveness" data-region="四区_上海" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="500" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">活体检测</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">500</div>
                                    <div class="qps-item-percentage">10%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 10%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 四区_广州 -->
                    <div class="region-card">
                        <div class="region-header">
                            <div class="region-name">四区_广州</div>
                            <div class="region-actions">
                                <button class="btn btn-primary" onclick="showEditRegionModal('四区_广州')">编辑区域</button>
                                <button class="btn btn-danger" onclick="confirmDeleteRegion('四区_广州')">删除区域</button>
                            </div>
                        </div>
                        <div class="service-stats">
                            <div class="service-item" data-service="face11" data-region="四区_广州" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="500" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">人脸1:1</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">500</div>
                                    <div class="qps-item-percentage">10%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 10%;"></div>
                                </div>
                            </div>
                            <div class="service-item" data-service="face1n" data-region="四区_广州" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="600" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">人脸1:N</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">600</div>
                                    <div class="qps-item-percentage">12%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 12%;"></div>
                                </div>
                            </div>
                            <div class="service-item" data-service="liveness" data-region="四区_广州" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="300" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">活体检测</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">300</div>
                                    <div class="qps-item-percentage">6%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 6%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 五区 -->
                    <div class="region-card">
                        <div class="region-header">
                            <div class="region-name">五区</div>
                            <div class="region-actions">
                                <button class="btn btn-primary" onclick="showEditRegionModal('五区')">编辑区域</button>
                                <button class="btn btn-danger" onclick="confirmDeleteRegion('五区')">删除区域</button>
                            </div>
                        </div>
                        <div class="service-stats">
                            <div class="service-item" data-service="face11" data-region="五区" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="0" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">人脸1:1</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">0</div>
                                    <div class="qps-item-percentage">0%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 0%;"></div>
                                </div>
                            </div>
                            <div class="service-item" data-service="face1n" data-region="五区" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="0" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">人脸1:N</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">0</div>
                                    <div class="qps-item-percentage">0%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 0%;"></div>
                                </div>
                            </div>
                            <div class="service-item" data-service="liveness" data-region="五区" onclick="selectServiceItem(this)">
                                <div class="qps-adjust-controls">
                                    <input type="number" class="qps-adjust-input" value="0" min="0" max="5000">
                                    <button class="qps-adjust-btn" onclick="applyQpsChange(this)">确定</button>
                                </div>
                                <div class="service-name">活体检测</div>
                                <div class="qps-item-header">
                                    <div class="qps-item-current">0</div>
                                    <div class="qps-item-percentage">0%</div>
                                </div>
                                <div class="qps-item-title">已分配 QPS</div>
                                <div class="qps-progress">
                                    <div class="qps-progress-fill" style="width: 0%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- QPS调配汇总面板 -->
        <div class="qps-summary-panel" id="qpsSummaryPanel">
            <div class="summary-title">
                🔄 QPS调配汇总
            </div>

            <div style="background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 6px; padding: 12px; margin-bottom: 16px; font-size: 12px; color: #1e40af;">
                <div style="font-weight: 600; margin-bottom: 4px;">💡 调配说明：</div>
                <div>• 点击任意服务项目进行QPS调配</div>
                <div>• 输入新的QPS值后点击"确定"</div>
                <div>• 总量限制：每种服务最多5,000 QPS</div>
            </div>

            <div class="summary-service">
                <div class="summary-service-name">人脸1:1 QPS</div>
                <div class="summary-stats">
                    <span class="summary-allocated">已分配: <span id="face11Total">3,500</span></span>
                    <span class="summary-remaining">剩余: <span id="face11Remaining">1,500</span></span>
                </div>
            </div>

            <div class="summary-service">
                <div class="summary-service-name">人脸1:N QPS</div>
                <div class="summary-stats">
                    <span class="summary-allocated">已分配: <span id="face1NTotal">2,400</span></span>
                    <span class="summary-remaining">剩余: <span id="face1NRemaining">2,600</span></span>
                </div>
            </div>

            <div class="summary-service">
                <div class="summary-service-name">活体检测 QPS</div>
                <div class="summary-stats">
                    <span class="summary-allocated">已分配: <span id="livenessTotal">1,600</span></span>
                    <span class="summary-remaining">剩余: <span id="livenessRemaining">3,400</span></span>
                </div>
            </div>

            <div class="summary-actions">
                <button class="summary-btn secondary" onclick="cancelAdjustMode()">取消</button>
                <button class="summary-btn primary" onclick="saveQpsChanges()">保存调配</button>
            </div>
        </div>
        
        <!-- QPS分配表格 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">服务器QPS分配列表</div>
                <div class="card-actions">
                    <select class="qps-input" id="regionFilter">
                        <option value="">全部区域</option>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                    <select class="qps-input" id="serviceFilter">
                        <option value="">全部服务</option>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-secondary">重置</button>
                </div>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>服务器ID</th>
                        <th>区域</th>
                        <th>服务</th>
                        <th>当前QPS / 分配QPS</th>
                        <th>授权服务状态</th>
                        <th>QPS下发状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>四区_北京_人脸1:1_face-service-bj-001</td>
                        <td>四区_北京</td>
                        <td><span class="tag tag-face">人脸1:1</span></td>
                        <td>450 / 500</td>
                        <td><span class="status-indicator status-online"></span>正常</td>
                        <td><span class="status-indicator status-online"></span>成功</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_北京_人脸1:1_face-service-bj-001')">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>四区_北京_人脸1:N_feature-service-bj-001</td>
                        <td>四区_北京</td>
                        <td><span class="tag tag-feature">人脸1:N</span></td>
                        <td>720 / 800</td>
                        <td><span class="status-indicator status-online"></span>正常</td>
                        <td><span class="status-indicator status-online"></span>成功</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_北京_人脸1:N_feature-service-bj-001')">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>四区_上海_活体检测_liveness-service-sh-001</td>
                        <td>四区_上海</td>
                        <td><span class="tag tag-liveness">活体检测</span></td>
                        <td>280 / 300</td>
                        <td><span class="status-indicator status-online"></span>正常</td>
                        <td><span class="status-indicator status-online"></span>成功</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_上海_活体检测_liveness-service-sh-001')">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>四区_广州_人脸1:1_face-service-gz-001</td>
                        <td>四区_广州</td>
                        <td><span class="tag tag-face">人脸1:1</span></td>
                        <td>0 / 300</td>
                        <td><span class="status-indicator status-offline"></span>离线</td>
                        <td><span class="status-indicator status-offline"></span>失败</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_广州_人脸1:1_face-service-gz-001')">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>四区_北京_活体检测_liveness-service-bj-001</td>
                        <td>四区_北京</td>
                        <td><span class="tag tag-liveness">活体检测</span></td>
                        <td>580 / 600</td>
                        <td><span class="status-indicator status-online"></span>正常</td>
                        <td><span class="status-indicator status-online"></span>成功</td>
                        <td>
                            <div class="qps-action">
                                <button class="btn btn-danger" onclick="confirmDeleteNode('四区_北京_活体检测_liveness-service-bj-001')">删除</button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 操作日志 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">操作日志</div>
                <div class="card-actions">
                    <select class="qps-input">
                        <option value="">全部区域</option>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                    <select class="qps-input">
                        <option value="">全部服务</option>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-secondary">导出</button>
                </div>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>日志ID</th>
                        <th>操作时间</th>
                        <th>区域</th>
                        <th>服务器ID</th>
                        <th>服务类型</th>
                        <th>操作类型</th>
                        <th>QPS变动</th>
                        <th>操作人</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>LOG00123</td>
                        <td>2025-04-08 14:32:15</td>
                        <td>四区_北京</td>
                        <td>四区_北京_人脸1:1_face-service-bj-001</td>
                        <td>人脸1:1</td>
                        <td>QPS修改</td>
                        <td>500 → 800</td>
                        <td>admin</td>
                    </tr>
                    <tr>
                        <td>LOG00122</td>
                        <td>2025-04-08 14:30:10</td>
                        <td>四区_北京</td>
                        <td>四区_北京_人脸1:N_feature-service-bj-001</td>
                        <td>人脸1:N</td>
                        <td>QPS修改</td>
                        <td>800 → 600</td>
                        <td>admin</td>
                    </tr>
                    <tr>
                        <td>LOG00121</td>
                        <td>2025-04-08 14:28:05</td>
                        <td>四区_上海</td>
                        <td>四区_上海_活体检测_liveness-service-sh-001</td>
                        <td>活体检测</td>
                        <td>新增区域</td>
                        <td>0 → 300</td>
                        <td>operator1</td>
                    </tr>
                    <tr>
                        <td>LOG00120</td>
                        <td>2025-04-08 14:25:18</td>
                        <td>四区_广州</td>
                        <td>四区_广州_人脸1:1_face-service-gz-001</td>
                        <td>人脸1:1</td>
                        <td>删除区域</td>
                        <td>300 → 0</td>
                        <td>operator2</td>
                    </tr>
                    <tr>
                        <td>LOG00119</td>
                        <td>2025-04-08 14:20:45</td>
                        <td>四区_北京</td>
                        <td>四区_北京_活体检测_liveness-service-bj-001</td>
                        <td>活体检测</td>
                        <td>QPS修改</td>
                        <td>400 → 600</td>
                        <td>admin</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 添加区域模态窗口 -->
    <div class="modal" id="addRegionModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">添加区域</div>
                <div class="modal-close" onclick="hideModal('addRegionModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">区域名称</label>
                    <input type="text" class="form-control" id="regionName" placeholder="请输入区域名称（如：四区_北京）">
                </div>
                <div class="form-group">
                    <label class="form-label">飞连 URL</label>
                    <input type="text" class="form-control" id="feilianUrl" placeholder="请输入飞连 URL">
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="enableFace11" onchange="toggleFace11Input()">
                        <label class="form-label" for="enableFace11">人脸1:1 QPS</label>
                        <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                            <span style="color: #8e94a3;">系统剩余可分配：</span>
                            <span style="color: #333; font-weight: 500;">1,500</span>
                        </div>
                    </div>
                    <input type="number" class="form-control" id="face11Qps" placeholder="请输入人脸1:1 QPS值" disabled>
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="enableFace1N" onchange="toggleFace1NInput()">
                        <label class="form-label" for="enableFace1N">人脸1:N QPS</label>
                        <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                            <span style="color: #8e94a3;">系统剩余可分配：</span>
                            <span style="color: #333; font-weight: 500;">1,600</span>
                        </div>
                    </div>
                    <input type="number" class="form-control" id="face1NQps" placeholder="请输入人脸1:N QPS值" disabled>
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="enableLiveness" onchange="toggleLivenessInput()">
                        <label class="form-label" for="enableLiveness">活体检测 QPS</label>
                        <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                            <span style="color: #8e94a3;">系统剩余可分配：</span>
                            <span style="color: #333; font-weight: 500;">1,800</span>
                        </div>
                    </div>
                    <input type="number" class="form-control" id="livenessQps" placeholder="请输入活体检测 QPS值" disabled>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('addRegionModal')">取消</button>
                <button class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>
    
    <!-- 添加节点模态窗口 -->
    <div class="modal" id="addNodeModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">添加服务器</div>
                <div class="modal-close" onclick="hideModal('addNodeModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">服务器ID</label>
                    <select class="form-select" id="addNodeId" onchange="handleNodeSelect()">
                        <option value="">请选择未分配服务器</option>
                        <option value="四区_北京_人脸1:1_face-service-bj-002" data-service="人脸1:1">四区_北京_人脸1:1_face-service-bj-002</option>
                        <option value="四区_上海_人脸1:N_feature-service-sh-002" data-service="人脸1:N">四区_上海_人脸1:N_feature-service-sh-002</option>
                        <option value="四区_广州_活体检测_liveness-service-gz-002" data-service="活体检测">四区_广州_活体检测_liveness-service-gz-002</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">所属区域</label>
                    <select class="form-select" id="addNodeRegion" onchange="updateAvailableQps()">
                        <option value="">请选择区域</option>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">服务类型</label>
                    <select class="form-select" id="addNodeService" disabled>
                        <option value="">请选择服务类型</option>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">QPS分配</label>
                    <input type="number" class="form-control" id="addNodeQps" placeholder="请输入QPS值">
                </div>
                <div class="qps-remaining">
                    <div class="qps-remaining-label">当前服务区域可分配QPS：</div>
                    <div class="qps-remaining-value" id="addNodeAvailableQps">-</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('addNodeModal')">取消</button>
                <button class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>
    
    <!-- 编辑节点模态窗口 -->
    <div class="modal" id="editNodeModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">编辑服务器</div>
                <div class="modal-close" onclick="hideModal('editNodeModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">服务器ID</label>
                    <input type="text" class="form-control" id="editNodeId" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">所属区域</label>
                    <select class="form-select" id="editNodeRegion" disabled>
                        <option value="四区_北京">四区_北京</option>
                        <option value="四区_上海">四区_上海</option>
                        <option value="四区_广州">四区_广州</option>
                        <option value="五区">五区</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">服务类型</label>
                    <select class="form-select" id="editNodeService" disabled>
                        <option value="人脸1:1">人脸1:1</option>
                        <option value="人脸1:N">人脸1:N</option>
                        <option value="活体检测">活体检测</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">QPS分配</label>
                    <input type="number" class="form-control" id="editNodeQps" placeholder="请输入QPS值" onchange="validateEditQps()">
                </div>
                <div class="qps-remaining">
                    <div class="qps-remaining-label">当前服务区域可分配QPS：</div>
                    <div class="qps-remaining-value" id="editNodeAvailableQps">-</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('editNodeModal')">取消</button>
                <button class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>
    
    <!-- 编辑区域模态窗口 -->
    <div class="modal" id="editRegionModal">
        <div class="modal-content" style="width: 600px;">
            <div class="modal-header">
                <div class="modal-title" id="editRegionTitle">编辑区域</div>
                <div class="modal-close" onclick="hideModal('editRegionModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">区域名称</label>
                    <input type="text" class="form-control" id="editRegionName" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">飞连 URL</label>
                    <input type="text" class="form-control" id="editFeilianUrl" placeholder="请输入飞连 URL">
                </div>
                <div class="service-stats-container">
                    <div class="form-group">
                        <div class="form-checkbox">
                            <input type="checkbox" id="editEnableFace11" onchange="toggleEditFace11Input()">
                            <label class="form-label" for="editEnableFace11">人脸1:1 QPS</label>
                            <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                                <span style="color: #8e94a3;">系统剩余可分配：</span>
                                <span style="color: #333; font-weight: 500;" id="editFace11Remaining">1,500</span>
                            </div>
                        </div>
                        <input type="number" class="form-control" id="editFace11Qps" placeholder="请输入人脸1:1 QPS值" disabled>
                    </div>
                    <div class="form-group">
                        <div class="form-checkbox">
                            <input type="checkbox" id="editEnableFace1N" onchange="toggleEditFace1NInput()">
                            <label class="form-label" for="editEnableFace1N">人脸1:N QPS</label>
                            <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                                <span style="color: #8e94a3;">系统剩余可分配：</span>
                                <span style="color: #333; font-weight: 500;" id="editFace1NRemaining">1,600</span>
                            </div>
                        </div>
                        <input type="number" class="form-control" id="editFace1NQps" placeholder="请输入人脸1:N QPS值" disabled>
                    </div>
                    <div class="form-group">
                        <div class="form-checkbox">
                            <input type="checkbox" id="editEnableLiveness" onchange="toggleEditLivenessInput()">
                            <label class="form-label" for="editEnableLiveness">活体检测 QPS</label>
                            <div class="qps-remaining" style="margin: 0; padding: 4px 8px; margin-left: auto;">
                                <span style="color: #8e94a3;">系统剩余可分配：</span>
                                <span style="color: #333; font-weight: 500;" id="editLivenessRemaining">1,800</span>
                            </div>
                        </div>
                        <input type="number" class="form-control" id="editLivenessQps" placeholder="请输入活体检测 QPS值" disabled>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('editRegionModal')">取消</button>
                <button class="btn btn-primary">保存修改</button>
            </div>
        </div>
    </div>
    
    <!-- 删除确认模态窗口 -->
    <div class="modal" id="deleteConfirmModal">
        <div class="modal-content" style="width: 400px;">
            <div class="modal-header">
                <div class="modal-title">确认删除</div>
                <div class="modal-close" onclick="hideModal('deleteConfirmModal')">×</div>
            </div>
            <div class="modal-body">
                <p>确定要删除服务器 <span id="deleteNodeId" style="font-weight: bold;"></span> 吗？此操作无法撤销。</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('deleteConfirmModal')">取消</button>
                <button class="btn btn-danger">确定删除</button>
            </div>
        </div>
    </div>

    <!-- 删除区域确认模态窗口 -->
    <div class="modal" id="deleteRegionConfirmModal">
        <div class="modal-content" style="width: 400px;">
            <div class="modal-header">
                <div class="modal-title">确认删除区域</div>
                <div class="modal-close" onclick="hideModal('deleteRegionConfirmModal')">×</div>
            </div>
            <div class="modal-body">
                <div class="delete-confirm-content">
                    <div class="delete-warning-icon">⚠️</div>
                    <div class="delete-warning-text">
                        <p>您确定要删除区域 <span id="deleteRegionId" style="font-weight: bold; color: #ff3b30;"></span> 吗？</p>
                        <p class="delete-warning-detail">此操作将：</p>
                        <ul class="delete-warning-list">
                            <li>删除该区域下的所有服务器配置</li>
                            <li>释放该区域的所有QPS配额</li>
                            <li>删除该区域的所有授权记录</li>
                        </ul>
                        <p class="delete-warning-note">注意：此操作无法撤销，请谨慎操作！</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal('deleteRegionConfirmModal')">取消</button>
                <button class="btn btn-danger" onclick="deleteRegion()">确定删除</button>
            </div>
        </div>
    </div>

    <script>
        // 模态窗口控制函数
        function showAddRegionModal() {
            document.getElementById('addRegionModal').classList.add('active');
        }
        
        function showAddNodeModal() {
            document.getElementById('addNodeModal').classList.add('active');
        }
        
        function showEditNodeModal(nodeId) {
            document.getElementById('editNodeId').value = nodeId;
            
            // 解析节点ID获取信息
            const [region1, region2, service] = nodeId.split('_');
            const region = region1 + '_' + region2;
            
            // 设置表单值
            document.getElementById('editNodeRegion').value = region;
            document.getElementById('editNodeService').value = service;
            
            // 设置当前QPS值
            let currentQps = 0;
            if (nodeId === '四区_北京_人脸1:1_face-service-bj-001') {
                currentQps = 500;
            } else if (nodeId === '四区_北京_人脸1:N_feature-service-bj-001') {
                currentQps = 800;
            }
            document.getElementById('editNodeQps').value = currentQps;
            
            // 计算并显示可用QPS
            updateEditAvailableQps(nodeId, currentQps);
            
            // 显示模态窗口
            document.getElementById('editNodeModal').classList.add('active');
        }
        
        function updateEditAvailableQps(nodeId, currentQps) {
            const [region1, region2, service] = nodeId.split('_');
            const region = region1 + '_' + region2;
            
            const qpsLimits = {
                '人脸1:1': {
                    total: 5000,
                    used: {
                        '四区_北京': 1800,
                        '四区_上海': 1200,
                        '四区_广州': 500,
                        '五区': 0
                    }
                },
                '人脸1:N': {
                    total: 3000,
                    used: {
                        '四区_北京': 1000,
                        '四区_上海': 800,
                        '四区_广州': 600,
                        '五区': 0
                    }
                },
                '活体检测': {
                    total: 2000,
                    used: {
                        '四区_北京': 800,
                        '四区_上海': 500,
                        '四区_广州': 300,
                        '五区': 0
                    }
                }
            };
            
            if (region && service && qpsLimits[service]) {
                const totalQps = qpsLimits[service].total;
                const usedQps = Object.values(qpsLimits[service].used).reduce((a, b) => a + b, 0);
                // 可用QPS需要加上当前节点的QPS（因为是编辑）
                const availableQps = totalQps - usedQps + currentQps;
                document.getElementById('editNodeAvailableQps').textContent = availableQps;
            }
        }

        function validateEditQps() {
            const qpsInput = document.getElementById('editNodeQps');
            const availableQps = parseInt(document.getElementById('editNodeAvailableQps').textContent);
            const inputQps = parseInt(qpsInput.value);
            
            if (inputQps > availableQps) {
                alert('输入的QPS超过可分配的最大值！');
                qpsInput.value = availableQps;
            } else if (inputQps < 0) {
                alert('QPS不能为负数！');
                qpsInput.value = 0;
            }
        }
        
        function showEditRegionModal(regionId) {
            // 设置模态窗口标题
            document.getElementById('editRegionTitle').textContent = '编辑区域 - ' + regionId;

            // 设置区域名称（只读）
            document.getElementById('editRegionName').value = regionId;

            // 填充飞连URL（这里可以根据实际数据填充）
            document.getElementById('editFeilianUrl').value = '';

            // 填充QPS数据并设置复选框状态
            let face11Qps = 0, face1NQps = 0, livenessQps = 0;
            switch(regionId) {
                case '四区_北京':
                    face11Qps = 1800;
                    face1NQps = 1000;
                    livenessQps = 800;
                    break;
                case '四区_上海':
                    face11Qps = 1200;
                    face1NQps = 800;
                    livenessQps = 500;
                    break;
                case '四区_广州':
                    face11Qps = 500;
                    face1NQps = 600;
                    livenessQps = 300;
                    break;
                case '五区':
                    face11Qps = 0;
                    face1NQps = 0;
                    livenessQps = 0;
                    break;
            }

            // 设置复选框状态和输入框值
            const face11Checkbox = document.getElementById('editEnableFace11');
            const face11Input = document.getElementById('editFace11Qps');
            if (face11Qps > 0) {
                face11Checkbox.checked = true;
                face11Input.disabled = false;
                face11Input.value = face11Qps;
            } else {
                face11Checkbox.checked = false;
                face11Input.disabled = true;
                face11Input.value = '';
            }

            const face1NCheckbox = document.getElementById('editEnableFace1N');
            const face1NInput = document.getElementById('editFace1NQps');
            if (face1NQps > 0) {
                face1NCheckbox.checked = true;
                face1NInput.disabled = false;
                face1NInput.value = face1NQps;
            } else {
                face1NCheckbox.checked = false;
                face1NInput.disabled = true;
                face1NInput.value = '';
            }

            const livenessCheckbox = document.getElementById('editEnableLiveness');
            const livenessInput = document.getElementById('editLivenessQps');
            if (livenessQps > 0) {
                livenessCheckbox.checked = true;
                livenessInput.disabled = false;
                livenessInput.value = livenessQps;
            } else {
                livenessCheckbox.checked = false;
                livenessInput.disabled = true;
                livenessInput.value = '';
            }

            // 更新剩余QPS显示
            updateEditRegionRemainingQps(regionId);

            // 显示模态窗口
            document.getElementById('editRegionModal').classList.add('active');
        }
        
        function confirmDeleteNode(nodeId) {
            document.getElementById('deleteNodeId').textContent = nodeId;
            document.getElementById('deleteConfirmModal').classList.add('active');
        }
        
        function hideModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
            }
        }

        function handleNodeSelect() {
            const nodeSelect = document.getElementById('addNodeId');
            const serviceSelect = document.getElementById('addNodeService');
            
            if (nodeSelect.value) {
                const selectedOption = nodeSelect.options[nodeSelect.selectedIndex];
                const serviceType = selectedOption.getAttribute('data-service');
                
                // 设置服务类型
                serviceSelect.value = serviceType;
                
                // 从节点ID中提取区域信息并自动设置
                const regionParts = nodeSelect.value.split('_');
                const region = regionParts[0] + '_' + regionParts[1];
                document.getElementById('addNodeRegion').value = region;
                
                // 更新可用QPS
                updateAvailableQps();
            } else {
                serviceSelect.value = '';
            }
        }

        function updateAvailableQps() {
            const region = document.getElementById('addNodeRegion').value;
            const service = document.getElementById('addNodeService').value;
            let availableQps = 0;
            
            // 根据区域和服务类型计算可用QPS
            const qpsLimits = {
                '人脸1:1': {
                    total: 5000,
                    used: {
                        '四区_北京': 1800,
                        '四区_上海': 1200,
                        '四区_广州': 500,
                        '五区': 0
                    }
                },
                '人脸1:N': {
                    total: 3000,
                    used: {
                        '四区_北京': 1000,
                        '四区_上海': 800,
                        '四区_广州': 600,
                        '五区': 0
                    }
                },
                '活体检测': {
                    total: 2000,
                    used: {
                        '四区_北京': 800,
                        '四区_上海': 500,
                        '四区_广州': 300,
                        '五区': 0
                    }
                }
            };
            
            if (region && service && qpsLimits[service]) {
                const totalQps = qpsLimits[service].total;
                const usedQps = Object.values(qpsLimits[service].used).reduce((a, b) => a + b, 0);
                availableQps = totalQps - usedQps;
            }
            
            document.getElementById('addNodeAvailableQps').textContent = availableQps;
        }

        function confirmDeleteRegion(regionId) {
            document.getElementById('deleteRegionId').textContent = regionId;
            document.getElementById('deleteRegionConfirmModal').classList.add('active');
        }

        function deleteRegion() {
            const regionId = document.getElementById('deleteRegionId').textContent;
            // TODO: 实现删除区域的逻辑
            alert('区域 ' + regionId + ' 已删除');
            hideModal('deleteRegionConfirmModal');
        }

        function bindServer(serverId) {
            // 打开添加服务器模态窗口
            document.getElementById('addNodeModal').classList.add('active');
            // 自动选中服务器ID
            const addNodeIdSelect = document.getElementById('addNodeId');
            if (addNodeIdSelect) {
                for (let i = 0; i < addNodeIdSelect.options.length; i++) {
                    if (addNodeIdSelect.options[i].value === serverId) {
                        addNodeIdSelect.selectedIndex = i;
                        break;
                    }
                }
                // 触发change事件，自动填充服务类型和区域
                addNodeIdSelect.onchange && addNodeIdSelect.onchange();
            }
        }

        // 控制人脸1:1 QPS输入框的启用/禁用
        function toggleFace11Input() {
            const checkbox = document.getElementById('enableFace11');
            const input = document.getElementById('face11Qps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        // 控制人脸1:N QPS输入框的启用/禁用
        function toggleFace1NInput() {
            const checkbox = document.getElementById('enableFace1N');
            const input = document.getElementById('face1NQps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        // 控制活体检测 QPS输入框的启用/禁用
        function toggleLivenessInput() {
            const checkbox = document.getElementById('enableLiveness');
            const input = document.getElementById('livenessQps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        // 编辑区域模态窗口的控制函数
        function toggleEditFace11Input() {
            const checkbox = document.getElementById('editEnableFace11');
            const input = document.getElementById('editFace11Qps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        function toggleEditFace1NInput() {
            const checkbox = document.getElementById('editEnableFace1N');
            const input = document.getElementById('editFace1NQps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        function toggleEditLivenessInput() {
            const checkbox = document.getElementById('editEnableLiveness');
            const input = document.getElementById('editLivenessQps');

            if (checkbox.checked) {
                input.disabled = false;
                input.focus();
            } else {
                input.disabled = true;
                input.value = '';
            }
        }

        // 更新编辑区域模态窗口的剩余QPS显示
        function updateEditRegionRemainingQps(currentRegionId) {
            // 这里可以根据实际需求计算剩余QPS
            // 暂时使用固定值，实际应用中应该动态计算
            document.getElementById('editFace11Remaining').textContent = '1,500';
            document.getElementById('editFace1NRemaining').textContent = '1,600';
            document.getElementById('editLivenessRemaining').textContent = '1,800';
        }

        // 初始化饼状图
        function initCharts() {
            // 通用图表配置
            const chartOptions = {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const percentage = ((value / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '50%'
            };

            // 人脸1:1 QPS饼状图
            const face11Ctx = document.getElementById('face11Chart').getContext('2d');
            new Chart(face11Ctx, {
                type: 'doughnut',
                data: {
                    labels: ['已分配', '剩余'],
                    datasets: [{
                        data: [3500, 1500],
                        backgroundColor: ['#2d78f4', '#e5eaf3'],
                        borderWidth: 0,
                        hoverBackgroundColor: ['#1c68e3', '#d9e1ef']
                    }]
                },
                options: chartOptions
            });

            // 人脸1:N QPS饼状图
            const face1NCtx = document.getElementById('face1NChart').getContext('2d');
            new Chart(face1NCtx, {
                type: 'doughnut',
                data: {
                    labels: ['已分配', '剩余'],
                    datasets: [{
                        data: [2400, 2600],
                        backgroundColor: ['#4cd964', '#e5eaf3'],
                        borderWidth: 0,
                        hoverBackgroundColor: ['#3eba54', '#d9e1ef']
                    }]
                },
                options: chartOptions
            });

            // 活体检测 QPS饼状图
            const livenessCtx = document.getElementById('livenessChart').getContext('2d');
            new Chart(livenessCtx, {
                type: 'doughnut',
                data: {
                    labels: ['已分配', '剩余'],
                    datasets: [{
                        data: [1600, 3400],
                        backgroundColor: ['#ff9500', '#e5eaf3'],
                        borderWidth: 0,
                        hoverBackgroundColor: ['#e6850e', '#d9e1ef']
                    }]
                },
                options: chartOptions
            });
        }

        // QPS调配模式相关功能
        let adjustMode = false;
        let selectedServiceItem = null;

        function toggleAdjustMode() {
            adjustMode = !adjustMode;
            const body = document.body;
            const btn = document.getElementById('adjustModeBtn');

            if (adjustMode) {
                body.classList.add('adjust-mode');
                btn.textContent = '❌ 退出调配模式';
                btn.classList.add('active');
            } else {
                body.classList.remove('adjust-mode');
                btn.textContent = '🔄 QPS调配模式';
                btn.classList.remove('active');
                if (selectedServiceItem) {
                    selectedServiceItem.classList.remove('selected');
                    selectedServiceItem = null;
                }
            }
        }

        function selectServiceItem(element) {
            if (!adjustMode) return;

            // 移除之前选中的项目
            if (selectedServiceItem) {
                selectedServiceItem.classList.remove('selected');
            }

            // 选中当前项目
            element.classList.add('selected');
            selectedServiceItem = element;

            // 阻止事件冒泡
            event.stopPropagation();
        }

        function applyQpsChange(button) {
            const input = button.previousElementSibling;
            const newValue = parseInt(input.value);
            const serviceItem = button.closest('.service-item');
            const service = serviceItem.dataset.service;
            const region = serviceItem.dataset.region;

            if (newValue < 0 || newValue > 5000) {
                alert('QPS值必须在0-5000之间');
                return;
            }

            // 更新显示
            const currentElement = serviceItem.querySelector('.qps-item-current');
            const percentageElement = serviceItem.querySelector('.qps-item-percentage');
            const progressFill = serviceItem.querySelector('.qps-progress-fill');

            currentElement.textContent = newValue.toLocaleString();
            const percentage = Math.round((newValue / 5000) * 100);
            percentageElement.textContent = percentage + '%';
            progressFill.style.width = percentage + '%';

            // 更新汇总面板
            updateSummaryPanel();

            // 取消选中
            serviceItem.classList.remove('selected');
            selectedServiceItem = null;

            // 阻止事件冒泡
            event.stopPropagation();
        }

        function updateSummaryPanel() {
            // 计算各服务的总分配量
            const face11Items = document.querySelectorAll('[data-service="face11"] .qps-item-current');
            const face1NItems = document.querySelectorAll('[data-service="face1n"] .qps-item-current');
            const livenessItems = document.querySelectorAll('[data-service="liveness"] .qps-item-current');

            let face11Total = 0, face1NTotal = 0, livenessTotal = 0;

            face11Items.forEach(item => {
                face11Total += parseInt(item.textContent.replace(/,/g, ''));
            });

            face1NItems.forEach(item => {
                face1NTotal += parseInt(item.textContent.replace(/,/g, ''));
            });

            livenessItems.forEach(item => {
                livenessTotal += parseInt(item.textContent.replace(/,/g, ''));
            });

            // 更新汇总显示
            document.getElementById('face11Total').textContent = face11Total.toLocaleString();
            document.getElementById('face11Remaining').textContent = (5000 - face11Total).toLocaleString();

            document.getElementById('face1NTotal').textContent = face1NTotal.toLocaleString();
            document.getElementById('face1NRemaining').textContent = (5000 - face1NTotal).toLocaleString();

            document.getElementById('livenessTotal').textContent = livenessTotal.toLocaleString();
            document.getElementById('livenessRemaining').textContent = (5000 - livenessTotal).toLocaleString();
        }

        function cancelAdjustMode() {
            // 重新加载页面或恢复原始数据
            location.reload();
        }

        function saveQpsChanges() {
            // 这里可以添加保存逻辑
            alert('QPS调配已保存！');
            toggleAdjustMode();
        }

        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
        });
    </script>


</body></html>